import { ethers , BigNumber, utils } from "ethers";

import bot from "../../bot";
import { macro } from "../macro";
import DataType, { BaseSwapExData, FindPathResult, MixGas, WalletData } from "../type/DataType";
import tools from "../tools";
import { Pair } from "../type/Pair";

export class MasterData {
    master : ethers.Wallet;
    bot : ethers.Contract;
    
    constructor(_master:ethers.Wallet, _bot:ethers.Contract){
        this.master = _master;
        this.bot = _bot;
    }
}

export default class BaseMaster {
    // @ts-ignore
    bull = new MasterData();
    // @ts-ignore
    bear = new MasterData();

    minGas : BigNumber = macro.bn.zero;

    constructor(){
        bot.mode = macro.BOT_MODE.LOCAL;
    }

    workflow(beforeOracle?:Function, afterPracle?:Function){

    }

    setMaster(bull : {address:string,key:string},bear : {address:string,key:string}){
        let websocketIdRemote = 0;
        for(let i = 0 ; i < bot.config.mainnet.wss.length; i++){
            if(!bot.config.mainnet.wss[i].includes("127")) {
                websocketIdRemote = i;
                break;
            }
        }
        const masterBull = bot.newWallet(bull.key, websocketIdRemote).ins();
        const contractBull = bot.newContract(bot.config.bot.address, macro.abi.bot,masterBull, websocketIdRemote).ins();
        this.bull = new MasterData(masterBull, contractBull);
        
        const masterBear = bot.newWallet(bear.key, websocketIdRemote).ins();
        const contractBear = bot.newContract(bot.config.bot2.address, macro.abi.bot, masterBear, websocketIdRemote).ins();
        this.bear = new MasterData(masterBear, contractBear);
        console.log("[setMaster] done")
        //console.log(this.bull);
    }

    async createAccounts(){
        let data : WalletData = {
            bull : {},
            bear : {},
            pump : {},
            oracle : {}
        }
        for(let i = 0; i < 31 ; i++){
            let d;
            if(i == 0){
                d = data.oracle;
            } else if(i<11) {
                d = data.bull;
            } else if(i<21){
                d = data.pump;
            } else {
                d = data.bear;
            }
            let wallet = ethers.Wallet.createRandom(); //.connect(provider)
            console.log(`[${i}] address: `, wallet.address);
            console.log("key: ", wallet.privateKey);
            //let b = await wallet.getBalance();
            //console.log("balance: ", b);
            d[wallet.address] = wallet.privateKey;
        }
        console.log(JSON.stringify(data));
    }

    async batchFeed(overrides = {}){
        console.log("batchFeed");
        let {list, total} = await bot.client.bull.checkFeed();
        if(list.length  == 0){
            console.log("no need to fill");
            return;
        }
        let res = await this.bull.bot.functions.feed(list, bot.config.eth, total, overrides);
        console.log(res);
        await res.wait();
        console.log("success");
    }

    /**
     * 把bull所有资产移动到新合约，读取balance_bull.json的资料
     */
    async movrBull(to : string, overrides = {}){
        await tools.delay(2000);
        console.log("[movrBull] begin...");
        const tokenBalance = bot.client.bull.tokenBalance;
        const tokens = Object.keys(tokenBalance.data());
        bot.config.stableTokens.forEach((t)=>{
            if(!tokens.includes(t)) tokens.push(t);
        });
        console.log("tokens: ", tokens);
        let res = await this.bull.bot.withdrawAllToEx(tokens, to, overrides);
        let receipt = await res.wait();
        console.log(receipt);
        console.log("[bullMove] move success");
    }

    /**
     * 把bull所有资产移动到新合约，读取balance_bull.json的资料
     */
    async movrBear(to : string, overrides = {}){
        const tokens = Object.keys(bot.config.tokens);
        let res = await this.bear.bot.withdrawAllToEx(tokens, to, overrides);
        console.log(await res.wait());
        console.log("[bearMove] move success");
    }

    private async feed(master:ethers.Wallet, wallets:Array<string>, maxNum:number, lowNum:number, gasPrice?:number){
        if(wallets.length == 0) return;
        const amount = utils.parseEther(maxNum.toString());
        const amountBase = utils.parseEther(lowNum.toString());

        console.log("----- feed begin ------");
        for(const v of wallets){
            //let wallet =  new ethers.Wallet(v as string, provider);
            
            let balance = await bot.provider().getBalance(v);
            console.log(`${v} balance: ` , utils.formatEther(balance));
            //console.log(amountBase.toString());
            //console.log(balance.toString());
            if(amountBase.gt(balance)){
                const a = amount.sub(balance);
                console.log("    feeding.... ", utils.formatEther(a));
                let data =  { to: v, value : a , gasLimit:21000};
                if(gasPrice) (data as any)["gasPrice"] = utils.parseUnits(gasPrice.toString(), 'gwei');
                let tx = await master.sendTransaction(data);
                let res = await tx.wait();   
                balance = await bot.provider().getBalance(v);
            }
            console.log(`     after: ` , utils.formatEther(balance));
        }
    }

    async addOperators(overwrite={}){
        console.log(`################## ${bot.chain} [addOperators] begin`);
        if(bot.config.pump.active){
            await this._addOperators("bull", Object.keys(bot.walletData.bull), this.bull.bot, overwrite);
            await this._addOperators("pump", Object.keys(bot.walletData.pump), this.bull.bot, overwrite);
        } else if(bot.config.trash.active || bot.config.bot.active){
            await this._addOperators("bull", Object.keys(bot.walletData.bull), this.bull.bot, overwrite);
        }
        if(bot.config.bot2.active){
            await this._addOperators("bear", Object.keys(bot.walletData.bear), this.bear.bot, overwrite);
        }
        console.log(`################## ${bot.chain} [addOperators] finish`);
    }

    async _addOperators(prefix:string, addresses:string[], contract:ethers.Contract, overwrite={}){
        let hasOps = true;
        for(let i = 0 ; i < addresses.length; i++){
            console.log(`[${prefix}] checking ops: ${i}`);
            let res = await contract.isOperator(addresses[i]);
            if(!res){
                console.log(` (${i}) is not operator`)
                hasOps = false;
                break;
            }
        }
        if(hasOps){
            console.log(`[${prefix}] already add operator.`);
            return;
        }
        let res2 = await contract.addOpts(addresses, overwrite);
        console.log(res2);
        console.log(`[${prefix}] pending`);
        console.log(await res2.wait());
        console.log(`[${prefix}] finish`);
    }


    async feedOperators(maxNum:number, lowNum:number, decimals=18, gasPrice?:number){
        
        if(bot.config.bot.active || bot.config.trash.active){
            console.log("----- feed bull ------");
            await this.feed(this.bull.master, Object.keys(bot.walletData.bull).slice(0, bot.config.maxOperator), maxNum, lowNum, gasPrice);
        }
        if(bot.config.bot2.active){
            console.log("----- feed bear ------");
            await this.feed(this.bear.master, Object.keys(bot.walletData.bear).slice(0, bot.config.maxOperator), maxNum / bot.config.feedPumpMulti, lowNum / bot.config.feedPumpMulti, gasPrice);
        }
        if(bot.config.pump.active){
            console.log("----- feed pump ------");
            await this.feed(this.bull.master, Object.keys(bot.walletData.pump).slice(0, bot.config.maxOperator), maxNum / bot.config.feedPumpMulti, lowNum / bot.config.feedPumpMulti, gasPrice);
        }
    }

    async withdrawEth(){
        const remain = utils.parseEther('0.1');
        
        console.log("----- [withdrawEth] pump -------");
        for(let k of Object.values(bot.walletData.pump)){
            let w = bot.newWallet(k).ins();
            let balance = await bot.provider().getBalance(w.address);
            console.log(w.address, ' balance: ', utils.formatEther(balance));
            if(balance.lt(remain)) continue;
            console.log(` withdrawing... ${utils.formatEther(balance.sub(remain))}`);
            const res = await w.sendTransaction({to: this.bear.master.address, value: balance.sub(remain), gasPrice: this.minGas, gasLimit:210000});
            let res2 = await res.wait();
            console.log(' after: ', utils.formatEther(await bot.provider().getBalance(w.address)));
        }

        console.log("----- [withdrawEth] bull -------");
        for(let k of Object.values(bot.walletData.bull)){
            let w = bot.newWallet(k).ins();
            let balance = await bot.provider().getBalance(w.address);
            console.log(w.address, ' balance: ', utils.formatEther(balance));
            if(balance.lt(remain)) continue;
            console.log(" withdrawing...");
            const res = await w.sendTransaction({to: this.bull.master.address, value: balance.sub(remain), gasPrice: this.minGas});
            let res2 = await res.wait();
            console.log(' after: ', utils.formatEther(await bot.provider().getBalance(w.address)));
        }
        
        
        console.log("----- [withdrawEth] bear -------");
        for(let k of Object.values(bot.walletData.bear)){
            let w = bot.newWallet(k).ins();
            let balance = await bot.provider().getBalance(w.address);
            console.log(w.address, ' balance: ', utils.formatEther(balance));
            if(balance.lt(remain)) continue;
            console.log(" withdrawing...");
            const res = await w.sendTransaction({to: this.bear.master.address, value: balance.sub(remain), gasPrice: this.minGas, gasLimit:210000});
            //const res = await w.sendTransaction({to: this.bear.master.address, value: balance.sub(remain), gasPrice: utils.parseUnits('2.5','gwei'), gasLimit:210000});
            let res2 = await res.wait();
            console.log(' after: ', utils.formatEther(await bot.provider().getBalance(w.address)));
        }
        console.log('width done');
    }

    async getGasInfo(){
        const gas = await bot.provider().getGasPrice();
        console.log(" gas now: ", utils.formatUnits(gas, 'gwei'));
        console.log(" min gas: ", bot.config.gasMin)
        console.log(" bot min gas: ", macro.abi.botGasSingleRouter * 4 * bot.config.gasMin * 0.000000001);
        console.log(" bull max gas: ", macro.abi.botGasSingleRouter * 4 * bot.config.gasMax * 0.000000001);
        console.log(" pump max gas: ", macro.abi.botPumpGasLimit * bot.config.pump.gasMax * 0.000000001);
        this.minGas = gas;

        return gas;
    }

    async getBearBalance(){
        console.log("----- getBearBalance --------");
        const color = macro.COLOR;

        const allTokens : string[] = [];
        Object.keys(bot.config.tokens).forEach( x => allTokens.push(x));
        console.log(` all tokens: ${allTokens.length}`);
        for(const addr of allTokens){
            const token = bot.token(addr);
            const balance = await this.bear.bot.getBalance(addr);
            const balanceNum = bot.token(addr).toNum(balance);
            if(balanceNum > 0) {
                const totalWeight = token.sum.token;
                const holdingP = balanceNum == 0 ? 0 : ((balanceNum / totalWeight) * 100).toFixed(2);
                const maxP = token.max == 0 ? 0 : ((token.max / totalWeight) * 100).toFixed(2);
                const bStable = bot.token(token.mainStableToken);
                const sell = await bot.client.bull.getBestAmountOut(addr, balance, false);
                console.log(` ## bear holding: ${bStable.symbol.padStart(5, " ")}:${sell.out.num.toFixed(2).padStart(10, " ")} ${color.Green}[${token.symbol.padEnd(8, " ")}]${color.Off} ${balanceNum.toFixed(3)}/${token.max}, ${(balanceNum >= token.max && token.max > 0) ? color.Green : color.White}${holdingP}%${color.Off} / ${maxP}%`);
            }
        }
    }

    async checkNonce(){
        //const allAc = Object.assign(bot.walletData.bull, bot.walletData.bear, bot.walletData.pump);
        const allAc = Object.assign(bot.walletData.bull, {});
        //const allAc = Object.assign(bot.walletData.pump, {});
        for(let [k,v] of Object.entries(allAc)){
            const w = bot.newWallet(v).ins();
            console.log(" ---- checking.... ", k, "-----------");

            let res = await w.sendTransaction({to:k, value:utils.parseEther('0.1'),  gasPrice:bot.handle.minGas()});
            //let res = await w.sendTransaction({to:k, value:utils.parseEther('0.1'), gasPrice: utils.parseUnits('50', 'gwei')});
            console.log("pending...");
            await res.wait();
            console.log("success!!");
        }
        console.log("all done");
    }

    async fixNonce(address:string, nonce:number, gas:number){
        const allAc = Object.assign(bot.walletData.pump, bot.walletData.bull, bot.walletData.bear);
        for(let [k,v] of Object.entries(allAc)){
            console.log(k);
            if(k.toLowerCase() == address.toLocaleLowerCase()){
                await this.fixNonceWithKey(v, nonce, gas);
                break;
            }
        }
    }

    async fixNonceWithKey(secrectkey:string, nonce:number, gas = bot.config.gasMin){
        const w = bot.newWallet(secrectkey).ins();
        const data = {
            to:w.address,
            value:utils.parseUnits('0.2', 18),
            nonce: nonce,
            gasPrice: utils.parseUnits(gas.toString(), 'gwei'),
            //gasPrice: 1,
            gasLimit:210000
        }
        let res = await w.sendTransaction(data);
        console.log("pending...");
        await res.wait();
        console.log("success!!");
    }


    async sellAllBullBalance(token?:string){
        console.log('sellAllBullBalance.....')
        await tools.delay(200);
        //const tokenBalance = bot.client.bull.tokenBalance;
        let tokenBalance = bot.client.bull.loadCache(macro.FILE.BALANCE_BULL);
        const tokens = Object.keys(tokenBalance.data());

        for(let i = 0 ; i < tokens.length; i++){
            const addr = tokens[i];
            if(bot.config.stableTokens.includes(addr)) continue;
            const balance = await bot.client.bull.getBalance(addr);
            if(balance.bn.eq(macro.bn.zero)) continue;
            this.sellToken(addr).then(r=>{ console.log(r)}).catch(e=>console.log(e));
        }

    }

    async sellToken(addr:string, min = 300)  {
        addr = addr.toLocaleLowerCase();
        return new Promise(async (resolve,reject)=>{
            //if(bot.config.stableTokens.includes(addr)) continue;
            const balance = await bot.client.bull.getBalance(addr);
            if(balance.bn.eq(macro.bn.zero)) reject('not enough balance');
            
            const sell = await bot.client.bull.getBestAmountOut(addr, balance.bn, false, 0.5, true);
            const outToken = bot.token(sell.paths[0][sell.paths[0].length - 1]);
            if(sell.out.num * outToken.price > min) reject();

            console.log(`sell ing ${bot.token(addr).symbol}: ${balance.num} -> ${sell.out.num}`);
            const data = new BaseSwapExData({
                data:sell,
                mixGas : new MixGas(),
                whale: bot.swapDataToWhale()
            });
            bot.client.bull.sellToken(data, (r)=>{
                resolve(r);
            });


        });
    }

    async getStableBalance(){
        let totalVal = 0;
        for(let stable of bot.config.stableTokens){
            let b = await bot.client.bull.getBalance(stable);
            console.log(`(${bot.config.tokens[stable].name.padStart(10, " ")}) ${b.num}`);
            totalVal += b.num * bot.token(stable).price;
        }
        console.log(`totalVal: ${totalVal.toFixed(2)}`);
    }


    async depositGas(amount: number, overwrite : Object = {}){
        console.log(" #####  depositGas #######");
        const gas = bot.newContract(bot.config.eth, macro.abi.token, this.bull.master, 0).ins();
        //const gas = new ethers.Contract(bot.config.eth, macro.abi.token, this.bull.master);
        //console.log(gas.functions);
        (overwrite as any).value = ethers.utils.parseEther(amount.toString());
        let res = await gas.deposit(overwrite);
        console.log("pending...");
        let result = await res.wait();
        console.log(result);
    }

    async withdrawGas(amount: number, overwrite: Object = {}) {
        console.log(" #####  withdrawGas #######");
        const gas = bot.newContract(bot.config.eth, macro.abi.token, this.bull.master, 0).ins();
        const weiAmount = ethers.utils.parseEther(amount.toString());
        let res = await gas.withdraw(weiAmount, overwrite);
        console.log("pending...");
        let result = await res.wait();
        console.log(result);
    }

    async batchCheckAllMev(d : {minReward?: number, group?:number, gwei? : number, bid?:number, chunk?:number}){
        const minReward = d.minReward || 0.005;
        const group = d.group || 6;
        const gwei = d.gwei;
        const delay = 200;
        const step = d.bid || 1;
        const chunk = d.chunk || 400;
        const sum = {cost:0, reward:0};

        if(this.minGas.isZero()) await this.getGasInfo();
        await bot.handle.nonce.updateAll();
        await tools.delay(1000);
        let mixGas = new MixGas();
        if(gwei){
            mixGas.set(utils.parseUnits(gwei.toString(), 'gwei'));
        } else {
            mixGas.set(this.minGas);
        }
        console.log(`gas : ${mixGas.display()}`);
        console.log(`gas more: ${mixGas.clone().more().display()}`);

        let i = -1 ;
        //let arr = tools.shuffleArray(Array.from(bot.oracleV2.data.pm.data.entries()));
        let arr = tools.shuffleArray(Array.from(bot.oracleV2.data.pm.data.values()));
        let total = arr.length;
        let cache : FindPathResult[] = [];
        let mevs : FindPathResult[] = [];
        console.log(`total mev: ${total}`);
        for(const v of arr){
            i++;
            v.mevPath.forEach( m =>{
                if(m.type !== macro.MEV_TYPE.DXDY) return;
                //计算cost
                let cost = mixGas.setGasLimit(BigNumber.from(m.gas)).getGasCost(m.s0).bn;
                /*
                if (cost.gt(macro.bn.u88_max.div(BigNumber.from(2)))){
                    console.log(`${macro.COLOR.Yellow} hit max uint88${macro.COLOR.Off}`);
                    cost = macro.bn.u88_max;
                }
                */
                if(m.gas > 4000000) {
                    //console.log(`${macro.COLOR.Blue} gas > 3000000 ${macro.COLOR.Off}`);
                    return;
                }
                if(m.status0 == macro.MEV_STATUS.ACTIVE){
                    const f0 = new FindPathResult();
                    f0.tokenIn0or1 = m.tokenIn0or1[0];
                    f0.convertEth = m.convertEth;
                    f0.fees = m.fees0;
                    f0.gas = m.gas;
                    f0.stable = m.s0;
                    f0.mevType = m.type;
                    f0.pairs = m.pairs;
                    f0.gasCost = cost;
                    cache.push(f0);
                }

                if(m.status1 == macro.MEV_STATUS.ACTIVE){
                    const f1 = new FindPathResult();
                    f1.tokenIn0or1 = m.tokenIn0or1[1];
                    f1.convertEth = m.convertEth;
                    f1.fees = m.fees1;
                    f1.gas = m.gas;
                    f1.stable = m.s1;
                    f1.mevType = m.type;
                    f1.pairs = [...m.pairs].reverse();
                    f1.gasCost = cost;
                    cache.push(f1);
                }
            });
            if((cache.length < chunk && i < total - 1) || cache.length == 0) continue;

            console.log(`(${i}/${total}) checking... len:${cache.length}`);
            let res = "";
            try {
                let s = DataType.findPathResultsToServerPumpData(cache);
                res = await bot.provider().call({
                    from: this.bull.master.address,
                    to : bot.config.bot.address,
                    data : s.encodePackedData,
                });
            } catch(e){
                console.log(e);
                console.log("### error findPathResults ###");
                //console.log(mevs);
                await tools.delay(1000);
                cache = [];
                continue;
                //process.exit();
            }

            let result = bot.contractIns.iBot.decodeFunctionResult("pumpSmart", res);
            let status  = result[0] as number[];
            let rewards = result[1] as BigNumber[];
            for(let j = 0; j < status.length; j++){
                if(status[j] == macro.PUMP_RESULTS.Success || status[j] == macro.PUMP_RESULTS.SuccessOverlow){
                    const token = bot.token(cache[j].stable);
                    const paths = bot.oracleV2.data.getTokenOuts(cache[j].stable, cache[j].pairs, true);
                    const reward = token.toNum(rewards[j]);
                    const rewardUsd = reward * token.price;
                    console.log(`${paths.join(' -> ')} reward: ${macro.COLOR.Green} (${token.symbol}) ${reward}${macro.COLOR.Off}`);
                    if(rewardUsd < minReward) {
                        continue;
                    } else if(mevs.some(_m=> _m.pairs.join(',') == cache[j].pairs.join(','))){
                        console.log(`${macro.COLOR.Blue}duplicate mev..${macro.COLOR.Off}`);
                        continue;
                    } else {
                        cache[j].reward = reward;
                        cache[j].rewardValue = rewardUsd;
                        mevs.push(cache[j]);
                        console.log(`${macro.COLOR.Yellow}JACKPOD!! : ${macro.COLOR.Off} (${mevs.length})`);
                    }
                } else if(status[j] == macro.PUMP_RESULTS.Balance0){
                    console.log(`${macro.COLOR.Red}err pair Balance0: ${macro.COLOR.Off}`, cache[j].pairs.join(','));
                } else if (status[j] == macro.PUMP_RESULTS.SwapErr){
                    console.log(`err pair SwapErr: `, cache[j].pairs.join(','));
                }
            }

            if(mevs.length >= group || (mevs.length > 0 && i >= total -1)){
                //const index = tools.randomInt(0, bot.config.maxOperator-1);
                const index = 0;
                const op = bot.client.trash.operators[index].ins().address;
                let nonce = bot.handle.nonce.get(op);
                let g = new MixGas().more();
                let resHash = "";
                let totalVal = 0;
                mevs.forEach(_m => totalVal += _m.rewardValue);
                console.log(`total reward: ${macro.COLOR.Yellow}${totalVal}${macro.COLOR.Off}`)
                for(let i = 0 ; i < step; i++){
                    console.log(`########    batch send request ${i}   #############`);
                    if(i > 0) g = g.clone().more();
                    const encode = DataType.findPathResultsToServerPumpData(mevs);
                    let signData1 = bot.client.trash.signTxSync(index,
                        encode.encodePackedData,
                        g,
                        4000000,
                        bot.config.bot.address,
                        nonce
                    );

                    //计算需要的gas是否盈利
                    if(step == 1){
                        let gasOnline = await bot.provider().estimateGas({
                            from: this.bull.master.address,
                            to: bot.config.bot.address,
                            data: encode.encodePackedData,
                            gasLimit: 30000000
                        });
                        const bEth = bot.token(bot.config.eth);
                        const costGas = Number(utils.formatEther(gasOnline.mul(mixGas.value())));
                        console.log(`gasOnline: ${gasOnline}, eth: ${costGas} val:${bEth.price * costGas}`);
                    }

                    try {
                        resHash = await bot.provider().send('eth_sendRawTransaction', [signData1]);
                    } catch(e){
                        console.log(e);
                        console.log("######## nonce error?   ########");
                        if(step == 1){
                            bot.handle.nonce.update(op);
                            await tools.delay(5000);
                            nonce = bot.handle.nonce.get(op);
                            i = -1;
                            continue;
                        }
                    }
                    console.log(` hash: ${resHash}`);
                    if(step == 1 ){
                        let receipt = await bot.provider().getTransactionReceipt(resHash);
                        for(let j = 0 ; j < 8; j++){
                            console.log(`fetching receipt....${j}`);
                            receipt = await bot.provider().getTransactionReceipt(resHash);
                            if(receipt){
                                break;
                            } else {
                                await tools.delay(bot.config.blockTime * 1000);
                            }
                        }
    
                        if(receipt){
                            let results : number[] = [];
                            let rewards : BigNumber[];
                            const logs = receipt["logs"] as Array<any>;
                            let status = receipt.status || 0;
                            if(status == 0){
                                //交易失败
                                console.log(`${macro.COLOR.BRed}FAIL TX${macro.COLOR.Off}`); //可能gas不够
                                continue;
                            } else {
                                if(logs && logs.length > 0){
                                    let logData = logs[logs.length-1].data;
                                    [ results, rewards ]= bot.abiCoder.decode(["uint8[]","uint112[]"], logData);
                                    //cost
                                    const { effectiveGasPrice, cumulativeGasUsed, gasUsed } = receipt;
                                    const bEth = bot.token(bot.config.eth);
                                    const costGas = bEth.toNum((effectiveGasPrice || cumulativeGasUsed).mul(gasUsed));
                                    const costVal = bEth.price * costGas;
                                    //reward
                                    let reward = 0;
                                    for(let j = 0 ; j < mevs.length; j++){
                                        if(rewards[j].isZero()) continue;
                                        const s = bot.token(mevs[j].stable);
                                        reward += s.toNum(rewards[j]) * s.price;
                                    }
                                    sum.cost += costVal;
                                    sum.reward += reward;

                                    console.log(`result: ${results.join(",")} val: ${rewards.map( r => utils.formatEther(r)).join(",")}`);
                                    console.log(`${macro.COLOR.Yellow}reward: $ ${reward.toFixed(3)} - ${costVal.toFixed(3)} = ${reward-costVal}${macro.COLOR.Off}`);
                                }
                            }
                        } else {
                            console.log(`${macro.COLOR.Red}bad tx${macro.COLOR.Off}`);
                        }
                    } else {
                        await tools.delay(delay);
                    }
                }
                mevs = [];
                bot.handle.nonce.use(op);
            }
            cache = [];
            //console.log(result);
        }
        console.log(`##################`);
        console.log(`### Results : $ ${sum.reward.toFixed(3)} - $ ${sum.cost.toFixed(3)} = ${macro.COLOR.BYellow}$ ${sum.reward - sum.cost}${macro.COLOR.Off}`);
        console.log("### batchCheckAllMev done ###");
        process.exit();
    }


    async findHoneyPool(){
        console.log("##### find honey pool begin ####");
        const cache = bot.oracleV2.data.cache;
        const pools : Pair[] = [];
        Object.values(cache.factorys).forEach( v => {
            v.pairs.forEach(p=>{
                const {f0,f1} = p;
                if(f0 == 666666 || f1 == 666666 || f0 == 800000 || f1 == 800000){
                    //console.log("honey: ", p);
                    pools.push(p);
                }
            });
        });
        pools.forEach(p=>{
            console.log(p);
        })
        console.log(`##### find honey pool end, total: ${pools.length} ####`);
    }

    /*
    convertTokenData(){
        const tokens : {[k:string]:any} = {};
        for(const [k,v] of Object.entries(bot.config.token)){
            let str = `"${v.address}" : { name: "${k}"`;
            if(v.ignore || !v.active) str += `, ignore: true`;
            if(v.max) str += `, max: ${v.max}`;
            if(v.whiteList) str += `, whiteList: true`;
            if(v.lazy) str += `, lazy: true`;
            str += ' },';
            console.log(str);
        }
    }
    */

}