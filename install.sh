#sudo su -
#sudo /etc/init.d/ssh restart
apt-get update &&
apt-get -y install curl screen rsync ruby wget unzip &&
curl https://raw.githubusercontent.com/creationix/nvm/master/install.sh | bash &&
source ~/.profile &&
curl -fsSL https://bun.sh/install | bash &&
source ~/.bashrc &&
nvm install 16.14.2 &&
nvm use 16.14.2 &&
nvm alias default node &&
npm install ts-node typescript -g &&
npm install pm2 -g &&

pm2 install typescript &&
pm2 install pm2-logrotate &&
pm2 set pm2-logrotate:max_size 5000K &&
pm2 set pm2-logrotate:retain 2 &&
pm2 startup && pm2 save

./node_modules/.bin/pm2 install typescript &&
./node_modules/.bin/pm2 install install pm2-logrotate &&
./node_modules/.bin/pm2 set pm2-logrotate:max_size 5000K &&
./node_modules/.bin/pm2 set pm2-logrotate:retain 3


#install go
wget https://go.dev/dl/go1.25.0.linux-amd64.tar.gz &&
rm -rf /usr/local/go && tar -C /usr/local -xzf go1.25.0.linux-amd64.tar.gz &&
export PATH=$PATH:/usr/local/go/bin &&
go version


# oktc fullnode
# https://www.okx.com/oktc/docs/dev/nodes/operation/how-to-start-mainnet-node